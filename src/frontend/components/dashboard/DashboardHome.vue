<template>
  <div class="space-y-6">
    <!-- Welcome Section -->
    <div class="bg-base-100 rounded-lg p-6 border border-base-300">
      <h1 class="text-2xl font-bold text-base-content mb-2">
        Welcome to EU Email Webhook
      </h1>
      <p class="text-base-content/70">
        Transform your emails into webhooks instantly. Get started by trying our test feature below.
      </p>
    </div>

    <!-- Test Email Card -->
    <TestEmailCard :user-id="userId" />

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div class="stat bg-base-100 rounded-lg border border-base-300">
        <div class="stat-figure text-primary">
          <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>
        <div class="stat-title">Domains</div>
        <div class="stat-value text-primary">{{ counts.domains || 0 }}</div>
        <div class="stat-desc">Email domains configured</div>
      </div>

      <div class="stat bg-base-100 rounded-lg border border-base-300">
        <div class="stat-figure text-secondary">
          <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        </div>
        <div class="stat-title">Webhooks</div>
        <div class="stat-value text-secondary">{{ counts.webhooks || 0 }}</div>
        <div class="stat-desc">Active webhook endpoints</div>
      </div>

      <div class="stat bg-base-100 rounded-lg border border-base-300">
        <div class="stat-figure text-accent">
          <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
          </svg>
        </div>
        <div class="stat-title">Aliases</div>
        <div class="stat-value text-accent">{{ counts.aliases || 0 }}</div>
        <div class="stat-desc">Email aliases created</div>
      </div>
    </div>

    <!-- Getting Started Guide -->
    <div class="bg-base-100 rounded-lg p-6 border border-base-300">
      <h2 class="text-xl font-semibold text-base-content mb-4">
        Getting Started
      </h2>
      
      <div class="space-y-4">
        <div class="flex items-start gap-4">
          <div class="flex-shrink-0 w-8 h-8 bg-primary text-primary-content rounded-full flex items-center justify-center font-bold text-sm">
            1
          </div>
          <div>
            <h3 class="font-medium text-base-content">Try the Test Feature</h3>
            <p class="text-base-content/70 text-sm">
              Send an email to your test address above to see how webhooks work instantly.
            </p>
          </div>
        </div>

        <div class="flex items-start gap-4">
          <div class="flex-shrink-0 w-8 h-8 bg-secondary text-secondary-content rounded-full flex items-center justify-center font-bold text-sm">
            2
          </div>
          <div>
            <h3 class="font-medium text-base-content">Add Your Domain</h3>
            <p class="text-base-content/70 text-sm">
              Configure your own domain to receive emails at your custom addresses.
            </p>
            <router-link to="/domains" class="link link-primary text-sm">
              Go to Domains →
            </router-link>
          </div>
        </div>

        <div class="flex items-start gap-4">
          <div class="flex-shrink-0 w-8 h-8 bg-accent text-accent-content rounded-full flex items-center justify-center font-bold text-sm">
            3
          </div>
          <div>
            <h3 class="font-medium text-base-content">Create Webhooks</h3>
            <p class="text-base-content/70 text-sm">
              Set up webhook endpoints to receive email data in your applications.
            </p>
            <router-link to="/webhooks" class="link link-primary text-sm">
              Go to Webhooks →
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-base-100 rounded-lg p-6 border border-base-300">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-xl font-semibold text-base-content">
          Recent Activity
        </h2>
        <router-link to="/logs" class="btn btn-outline btn-sm">
          View All Logs
        </router-link>
      </div>
      
      <div class="text-center py-8 text-base-content/50">
        <svg class="w-12 h-12 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <p>No recent email activity</p>
        <p class="text-sm">Send an email to your test address to get started!</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useMetrics } from '@composables/useMetrics'
import TestEmailCard from '../onboarding/TestEmailCard.vue'

// Get user ID from auth check
const userId = ref<string>('')

// Use metrics composable for counts
const { counts, loadMetrics } = useMetrics()

// Load user info and metrics
const loadUserInfo = async () => {
  try {
    const response = await fetch('/api/auth/check', {
      credentials: 'include'
    })
    
    if (response.ok) {
      const data = await response.json()
      userId.value = data.user.id
    }
  } catch (error) {
    console.error('Failed to load user info:', error)
  }
}

onMounted(async () => {
  await Promise.all([
    loadUserInfo(),
    loadMetrics()
  ])
})
</script>

<style scoped>
.stat {
  padding: 1.5rem;
}

.stat-figure {
  margin-bottom: 0.5rem;
}
</style>
